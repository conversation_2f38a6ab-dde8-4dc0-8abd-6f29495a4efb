using System.Text;
using HtmlAgilityPack;

namespace Dashboard_Yuntech.Service
{
    public class WebScrapingService
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public WebScrapingService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 爬取 Cacti 網頁並獲取 CSV 下載連結
        /// </summary>
        /// <param name="url">Cacti 圖表頁面 URL</param>
        /// <param name="timeRange">時間範圍 (Hourly, Daily, Weekly, Monthly, Yearly)</param>
        /// <returns>CSV 下載 URL</returns>
        public async Task<string> GetCsvDownloadUrlAsync(string url, string timeRange = "Hourly")
        {
            try
            {
                var client = _httpClientFactory.CreateClient();

                // 設置 User-Agent 避免被阻擋
                client.DefaultRequestHeaders.Add("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"無法訪問網頁: {response.StatusCode}");
                }

                var html = await response.Content.ReadAsStringAsync();

                // 使用 HtmlAgilityPack 解析 HTML
                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                // 找到所有包含 graph_xport.php 的連結
                var csvLinks = doc.DocumentNode
                    .SelectNodes("//a[@href and contains(@href, 'graph_xport.php')]");

                if (csvLinks == null || csvLinks.Count == 0)
                {
                    throw new Exception("找不到 CSV 下載連結");
                }

                // 根據時間範圍選擇對應的連結
                HtmlNode? targetLink = null;
                string targetRraId = "";

                // 根據時間範圍確定對應的 rra_id
                switch (timeRange)
                {
                    case "Hourly":
                        targetRraId = "rra_id=5";
                        break;
                    case "Daily":
                        targetRraId = "rra_id=1";
                        break;
                    case "Weekly":
                        targetRraId = "rra_id=2";
                        break;
                    case "Monthly":
                        targetRraId = "rra_id=3";
                        break;
                    case "Yearly":
                        targetRraId = "rra_id=4";
                        break;
                    default:
                        targetRraId = "rra_id=5"; // 預設為 Hourly
                        break;
                }

                // 找到包含指定 rra_id 的連結
                foreach (var link in csvLinks)
                {
                    var href = link.GetAttributeValue("href", "");
                    if (href.Contains(targetRraId))
                    {
                        targetLink = link;
                        break;
                    }
                }

                // 如果找不到指定時間範圍的連結，使用第一個連結
                if (targetLink == null)
                {
                    targetLink = csvLinks.First();
                }

                var selectedCsvUrl = targetLink.GetAttributeValue("href", "");

                // 解碼 HTML 實體，將 &amp; 轉換為 &
                selectedCsvUrl = System.Net.WebUtility.HtmlDecode(selectedCsvUrl);

                // 如果是相對路徑，轉換為絕對路徑
                if (selectedCsvUrl.StartsWith("/") || !selectedCsvUrl.StartsWith("http"))
                {
                    var baseUri = new Uri(url);
                    var fullUrl = new Uri(baseUri, selectedCsvUrl).ToString();
                    return fullUrl;
                }

                return selectedCsvUrl;
            }
            catch (Exception ex)
            {
                throw new Exception($"爬取網頁時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 下載 CSV 資料
        /// </summary>
        /// <param name="csvUrl">CSV 下載 URL</param>
        /// <returns>CSV 內容</returns>
        public async Task<string> DownloadCsvAsync(string csvUrl)
        {
            try
            {
                var client = _httpClientFactory.CreateClient();
                
                // 設置 User-Agent
                client.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

                var response = await client.GetAsync(csvUrl);
                
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"下載 CSV 失敗: {response.StatusCode}");
                }

                var csvContent = await response.Content.ReadAsStringAsync();
                return csvContent;
            }
            catch (Exception ex)
            {
                throw new Exception($"下載 CSV 時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 爬取網頁並直接下載 CSV 資料
        /// </summary>
        /// <param name="url">Cacti 圖表頁面 URL</param>
        /// <param name="timeRange">時間範圍</param>
        /// <returns>CSV 內容</returns>
        public async Task<string> ScrapeAndDownloadCsvAsync(string url, string timeRange = "Hourly")
        {
            var csvUrl = await GetCsvDownloadUrlAsync(url, timeRange);
            return await DownloadCsvAsync(csvUrl);
        }

        /// <summary>
        /// 解析 CSV 內容為資料表格式，只擷取 "Date" 行以下的資料
        /// </summary>
        /// <param name="csvContent">CSV 內容</param>
        /// <returns>解析後的資料</returns>
        public List<Dictionary<string, object>> ParseCsvContent(string csvContent)
        {
            var result = new List<Dictionary<string, object>>();
            
            if (string.IsNullOrWhiteSpace(csvContent))
                return result;

            var lines = csvContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            
            if (lines.Length < 2)
                return result;

            // 找到包含 "Date" 的標題行
            int headerLineIndex = -1;
            string[] headers = null;
            
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                // 檢查是否包含 Date、Inbound、Outbound 這三個欄位
                if (line.Contains("Date", StringComparison.OrdinalIgnoreCase) &&
                    line.Contains("Inbound", StringComparison.OrdinalIgnoreCase) &&
                    line.Contains("Outbound", StringComparison.OrdinalIgnoreCase))
                {
                    headerLineIndex = i;
                    // 使用 tab 或 comma 分隔
                    if (line.Contains('\t'))
                    {
                        headers = line.Split('\t').Select(h => h.Trim('"').Trim()).ToArray();
                    }
                    else
                    {
                        headers = line.Split(',').Select(h => h.Trim('"').Trim()).ToArray();
                    }
                    break;
                }
            }
            
            // 如果找不到正確的標題行，回傳空結果
            if (headerLineIndex == -1 || headers == null)
            {
                return result;
            }
            
            // 處理 Date 行以下的資料行
            for (int i = headerLineIndex + 1; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                if (string.IsNullOrEmpty(line))
                    continue;
                    
                // 使用相同的分隔符號解析資料
                string[] values;
                if (line.Contains('\t'))
                {
                    values = line.Split('\t');
                }
                else
                {
                    values = line.Split(',');
                }
                
                var row = new Dictionary<string, object>();
                
                for (int j = 0; j < Math.Min(headers.Length, values.Length); j++)
                {
                    var value = values[j].Trim('"').Trim();
                    
                    // 如果是空值，跳過
                    if (string.IsNullOrEmpty(value))
                        continue;
                    
                    // 檢查是否為 NaN 值（字串形式）
                    if (string.Equals(value, "NaN", StringComparison.OrdinalIgnoreCase) ||
                        string.Equals(value, "nan", StringComparison.OrdinalIgnoreCase))
                    {
                        // 跳過 NaN 值，不加入到 row 中
                        continue;
                    }
                    
                    // 嘗試轉換為整數，但要排除 NaN 值
                    if (double.TryParse(value, out double numValue) && !double.IsNaN(numValue) && !double.IsInfinity(numValue))
                    {
                        // 轉換為整數
                        row[headers[j]] = (int)Math.Round(numValue);
                    }
                    else if (DateTime.TryParse(value, out DateTime dateValue))
                    {
                        row[headers[j]] = dateValue;
                    }
                    else
                    {
                        // 對於無法解析的字串值，也要檢查是否包含 NaN
                        if (!value.Contains("NaN", StringComparison.OrdinalIgnoreCase))
                        {
                            row[headers[j]] = value;
                        }
                        // 如果包含 NaN，就跳過不加入
                    }
                }
                
                // 檢查這一行是否包含必要的欄位且沒有 NaN 值
                var hasValidInbound = false;
                var hasValidOutbound = false;
                var hasValidDate = false;
                
                foreach (var key in row.Keys)
                {
                    if (key.Contains("Date", StringComparison.OrdinalIgnoreCase))
                        hasValidDate = true;
                    else if (key.Contains("Inbound", StringComparison.OrdinalIgnoreCase))
                        hasValidInbound = true;
                    else if (key.Contains("Outbound", StringComparison.OrdinalIgnoreCase))
                        hasValidOutbound = true;
                }
                
                // 只有當資料行有內容且包含必要欄位時才加入
                if (row.Count > 0 && hasValidDate && hasValidInbound && hasValidOutbound)
                {
                    result.Add(row);
                }
            }
            
            return result;
        }

        /// <summary>
        /// 擷取 CSV 資料中第一行的日期部分（僅日期，不含時間）
        /// </summary>
        /// <param name="csvContent">CSV 內容</param>
        /// <returns>日期字串，格式為 yyyy/M/d</returns>
        public string GetFirstDataDate(string csvContent)
        {
            var csvData = ParseCsvContent(csvContent);
            
            if (csvData == null || !csvData.Any())
                return "";

            var firstRow = csvData.First();
            
            // 尋找 Date 欄位
            var dateKey = firstRow.Keys.FirstOrDefault(k => k.Contains("Date", StringComparison.OrdinalIgnoreCase));
            
            if (dateKey == null || !firstRow.ContainsKey(dateKey))
                return "";

            var dateValue = firstRow[dateKey];
            
            if (dateValue is DateTime dateTime)
            {
                return dateTime.ToString("yyyy/M/d");
            }
            else if (dateValue is string dateString && DateTime.TryParse(dateString, out DateTime parsedDate))
            {
                return parsedDate.ToString("yyyy/M/d");
            }
            
            return "";
        }

        /// <summary>
        /// 將 CSV 資料轉換為網路流量圖表資料
        /// </summary>
        /// <param name="csvContent">CSV 內容</param>
        /// <param name="timeRange">時間範圍，用於決定聚合間隔</param>
        /// <param name="aggregateMinutes">聚合間隔（分鐘），0 表示不聚合</param>
        /// <returns>圖表資料列表</returns>
        public List<Dashboard_Yuntech.Models.ChartModels.NetworkTrafficModel> ConvertToChartData(string csvContent, string timeRange = "Hourly", int aggregateMinutes = 0)
        {
            var result = new List<Dashboard_Yuntech.Models.ChartModels.NetworkTrafficModel>();
            var csvData = ParseCsvContent(csvContent);
            
            if (csvData == null || !csvData.Any())
                return result;

            // 根據時間範圍自動決定聚合間隔 - 全部改為不聚合，保留所有原始資料
            if (aggregateMinutes == 0)
            {
                aggregateMinutes = timeRange switch
                {
                    "Hourly" => 0,     // 每小時圖表：不聚合，保留所有原始1分鐘間隔資料
                    "Daily" => 0,      // 每日圖表：不聚合，保留所有原始5分鐘間隔資料
                    "Weekly" => 0,     // 每週圖表：不聚合，保留所有原始30分鐘間隔資料
                    "Monthly" => 0,    // 每月圖表：不聚合，保留所有原始2小時間隔資料
                    "Yearly" => 0,     // 每年圖表：不聚合，保留所有原始1天間隔資料
                    _ => 0             // 其他情況不聚合
                };
            }

            var rawData = new List<Dashboard_Yuntech.Models.ChartModels.NetworkTrafficModel>();

            // 先處理所有原始資料
            foreach (var row in csvData)
            {
                try
                {
                    // 尋找 Date 欄位
                    var dateKey = row.Keys.FirstOrDefault(k => k.Contains("Date", StringComparison.OrdinalIgnoreCase));
                    var inboundKey = row.Keys.FirstOrDefault(k => k.Contains("Inbound", StringComparison.OrdinalIgnoreCase));
                    var outboundKey = row.Keys.FirstOrDefault(k => k.Contains("Outbound", StringComparison.OrdinalIgnoreCase));

                    if (dateKey == null || inboundKey == null || outboundKey == null)
                        continue;

                    // 檢查 Inbound 和 Outbound 是否包含有效數值（排除 NaN）
                    if (!row.ContainsKey(inboundKey) || !row.ContainsKey(outboundKey))
                        continue;

                    // 檢查是否為 NaN 或無效數值
                    var inboundValue = row[inboundKey];
                    var outboundValue = row[outboundKey];
                    
                    // 如果是 NaN 字串或無效數值，跳過這一列
                    if (IsInvalidNumericValue(inboundValue) || IsInvalidNumericValue(outboundValue))
                        continue;

                    // 取得原始日期時間
                    DateTime originalDateTime = DateTime.MinValue;
                    if (row[dateKey] is DateTime dateTime)
                    {
                        originalDateTime = dateTime;
                    }
                    else if (row[dateKey] is string dateString && DateTime.TryParse(dateString, out DateTime parsedDate))
                    {
                        originalDateTime = parsedDate;
                    }

                    if (originalDateTime == DateTime.MinValue)
                        continue;

                    // 使用包含年月日的格式（yyyy mm dd hh mm）
                    string timeOnly = originalDateTime.ToString("yyyy MM dd HH mm");

                    // 取得流量數值
                    int inbound = 0;
                    int outbound = 0;

                    if (row[inboundKey] is int inboundInt)
                        inbound = inboundInt;
                    else if (int.TryParse(row[inboundKey]?.ToString(), out int inboundParsed))
                        inbound = inboundParsed;

                    if (row[outboundKey] is int outboundInt)
                        outbound = outboundInt;
                    else if (int.TryParse(row[outboundKey]?.ToString(), out int outboundParsed))
                        outbound = outboundParsed;

                    var chartData = new Dashboard_Yuntech.Models.ChartModels.NetworkTrafficModel
                    {
                        Time = timeOnly,
                        Inbound = inbound,
                        Outbound = outbound,
                        OriginalDateTime = originalDateTime,
                        OriginalTimeFormat = originalDateTime.ToString("yyyy/M/d HH:mm")
                    };

                    rawData.Add(chartData);
                }
                catch (Exception ex)
                {
                    // 跳過有問題的資料行
                    Console.WriteLine($"處理圖表資料時發生錯誤: {ex.Message}");
                    continue;
                }
            }

            // 按照原始時間排序
            rawData = rawData.OrderBy(x => x.OriginalDateTime).ToList();

            // 如果不需要聚合，直接返回原始資料
            if (aggregateMinutes <= 0 || !rawData.Any())
                return rawData;

            // 進行時間聚合
            result = AggregateDataByTimeInterval(rawData, aggregateMinutes);

            return result;
        }

        /// <summary>
        /// 將資料按時間間隔聚合，計算平均值
        /// </summary>
        /// <param name="rawData">原始資料</param>
        /// <param name="intervalMinutes">聚合間隔（分鐘）</param>
        /// <returns>聚合後的資料</returns>
        private List<Dashboard_Yuntech.Models.ChartModels.NetworkTrafficModel> AggregateDataByTimeInterval(
            List<Dashboard_Yuntech.Models.ChartModels.NetworkTrafficModel> rawData, int intervalMinutes)
        {
            var result = new List<Dashboard_Yuntech.Models.ChartModels.NetworkTrafficModel>();

            if (!rawData.Any())
                return result;

            // 以第一筆資料的實際時間為基準，建立時間區間
            var baseTime = rawData.First().OriginalDateTime;
            // 使用實際資料的起始時間，而不是整點時間
            var startTime = baseTime;

            // 將資料按時間區間分組
            var groups = rawData.GroupBy(data =>
            {
                var timeDiff = data.OriginalDateTime - startTime;
                var intervalIndex = (int)(timeDiff.TotalMinutes / intervalMinutes);
                return intervalIndex;
            });

            foreach (var group in groups.OrderBy(g => g.Key))
            {
                var groupData = group.ToList();
                if (!groupData.Any())
                    continue;

                // 計算平均值
                var avgInbound = (int)groupData.Average(d => d.Inbound);
                var avgOutbound = (int)groupData.Average(d => d.Outbound);

                // 使用區間的起始時間作為代表時間
                var intervalStartTime = startTime.AddMinutes(group.Key * intervalMinutes);

                var aggregatedData = new Dashboard_Yuntech.Models.ChartModels.NetworkTrafficModel
                {
                    Time = intervalStartTime.ToString("yyyy MM dd HH mm"),
                    Inbound = avgInbound,
                    Outbound = avgOutbound,
                    OriginalDateTime = intervalStartTime,
                    OriginalTimeFormat = intervalStartTime.ToString("yyyy/M/d HH:mm")
                };

                result.Add(aggregatedData);
            }

            return result;
        }

        /// <summary>
        /// 檢查數值是否為無效值（NaN、Infinity 或無法解析的值）
        /// </summary>
        /// <param name="value">要檢查的值</param>
        /// <returns>如果是無效值則返回 true</returns>
        private bool IsInvalidNumericValue(object value)
        {
            if (value == null)
                return true;

            var stringValue = value.ToString();
            
            // 檢查是否為 NaN 字串
            if (string.Equals(stringValue, "NaN", StringComparison.OrdinalIgnoreCase) ||
                string.Equals(stringValue, "nan", StringComparison.OrdinalIgnoreCase))
                return true;

            // 嘗試解析為 double 並檢查是否為 NaN 或 Infinity
            if (double.TryParse(stringValue, out double numValue))
            {
                return double.IsNaN(numValue) || double.IsInfinity(numValue);
            }

            // 如果無法解析為數值，也視為無效
            return true;
        }
    }
}
