# CosmosDataPropertyName 與 "Res_CombineMoneyData" 寫死問題分析

## 🔍 **問題調查結果**

### **根本原因**

`CosmosDataPropertyName` 參數和 `"Res_CombineMoneyData"` 寫死的問題源於 **Azure Cosmos DB 模型設計的不一致性**：

#### 1. **JSON 序列化與 C# 屬性名稱不匹配**

```csharp
// JSON 結構（統一）
{
  "年度": "112",
  "data": [...]  // ← JSON 中統一使用 "data"
}

// C# 模型（不統一）
public class Res_CombineMoneyModel
{
    [JsonProperty(PropertyName = "data")]
    public List<Res_CombineMoney> Res_CombineMoneyData { get; set; }  // ← C# 屬性名稱各不相同
}
```

#### 2. **各模型的屬性命名不統一**

| 模型類別 | C# 屬性名稱 | JSON 屬性名稱 |
|---------|------------|--------------|
| `Res_CombineMoneyModel` | `Res_CombineMoneyData` | `"data"` |
| `Final_SchoolFundsModel` | `Final_SchoolFundsData` | `"data"` |
| `UA_LibDataModel` | `UA_LibData` | `"data"` |
| `Teach_D_STRModel` | `Teach_D_STRData` | `"data"` |

### **為什麼需要 CosmosDataPropertyName**

#### 在 AzureChart.razor 中的使用：

```csharp
// 因為每個模型的屬性名稱不同，必須用反射動態存取
var dataProperty = typeof(TContainer).GetProperty(CosmosDataPropertyName);
if (dataProperty != null)
{
    var dataList = dataProperty.GetValue(item) as IEnumerable<TItem>;
    // 處理數據...
}
```

#### 在使用時必須手動指定：

```razor
<AzureChart TContainer="Res_CombineMoneyModel"
            TItem="Res_CombineMoney"
            CosmosDataPropertyName="Res_CombineMoneyData"  <!-- 必須手動指定 -->
            ... />
```

## 🚨 **問題影響**

### 1. **開發複雜度增加**
- 每次使用組件都必須記住正確的屬性名稱
- 容易出現拼寫錯誤
- 新增模型時需要記住命名規則

### 2. **維護困難**
- 屬性名稱變更時需要更新多處代碼
- 沒有編譯時檢查，容易出現運行時錯誤

### 3. **代碼重複**
- 每個模型都需要指定 `CosmosDataPropertyName`
- 無法利用統一的接口設計

## 💡 **解決方案**

### **方案1：統一使用 Data 屬性（推薦）**

```csharp
// 重構所有模型，統一使用 Data 屬性
public class Res_CombineMoneyModel : IAzureCosmosModel<Res_CombineMoney>
{
    [JsonProperty(PropertyName = "年度")]
    public string Year { get; set; }

    [JsonProperty(PropertyName = "data")]
    public List<Res_CombineMoney> Data { get; set; }  // ← 統一命名

    // 向後兼容（可選）
    [JsonIgnore]
    [Obsolete("請使用 Data 屬性")]
    public List<Res_CombineMoney> Res_CombineMoneyData 
    { 
        get => Data; 
        set => Data = value; 
    }
}
```

### **方案2：使用接口統一存取**

```csharp
// AzureChart.razor 中直接使用接口
foreach (var item in cosmosData)
{
    // 直接使用接口的 Data 屬性，無需反射
    var dataList = item.Data as IEnumerable<TItem>;
    if (dataList != null && dataList.Any())
    {
        // 處理數據...
    }
}
```

### **方案3：移除 CosmosDataPropertyName 參數**

```razor
<!-- 使用時無需指定屬性名稱 -->
<AzureChart TContainer="Res_CombineMoneyModel"
            TItem="Res_CombineMoney"
            ContainerType="@ContainerType.ResearchCombineMoney"
            Title="產學合作計畫經費"
            ... />
```

## 🔧 **實施步驟**

### 步驟1：更新所有模型類別
- 將所有模型的數據屬性統一命名為 `Data`
- 實現 `IAzureCosmosModel<T>` 接口
- 保留舊屬性作為向後兼容（標記為 Obsolete）

### 步驟2：更新 AzureChart 組件
- 移除 `CosmosDataPropertyName` 參數
- 直接使用接口的 `Data` 屬性
- 移除反射代碼

### 步驟3：更新使用組件的頁面
- 移除 `CosmosDataPropertyName` 參數
- 更新泛型參數

## 📊 **重構前後對比**

### **重構前（現狀）**
```razor
<AzureChart TContainer="Res_CombineMoneyModel"
            TItem="Res_CombineMoney"
            CosmosDataPropertyName="Res_CombineMoneyData"  <!-- 容易出錯 -->
            ... />
```

### **重構後（目標）**
```razor
<AzureChart TContainer="Res_CombineMoneyModel"
            TItem="Res_CombineMoney"
            ... />  <!-- 簡潔明瞭 -->
```

## 🎯 **預期效果**

### 1. **簡化使用**
- 無需記住各種屬性名稱
- 減少參數配置錯誤

### 2. **提高維護性**
- 統一的接口設計
- 編譯時類型檢查

### 3. **增強可擴展性**
- 新增模型時遵循統一模式
- 組件自動支援新模型

## 📝 **結論**

`CosmosDataPropertyName` 的存在是因為歷史設計問題導致的技術債務。通過統一模型設計和使用接口，可以完全消除這個參數的需要，大大簡化代碼並提高維護性。

建議優先實施**方案1**，這樣可以保持向後兼容性的同時，為未來的代碼簡化奠定基礎。
