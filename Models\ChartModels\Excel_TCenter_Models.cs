using Dashboard_Yuntech.Service;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace Dashboard_Yuntech.Models.ChartModels
{
    /// <summary>
    /// 校務類-圖書館
    /// </summary>
    public class LibModelNew
    {
        public string SchoolName { get; set; }     // 學校名稱

        [ChartColumn(ChartColumnType.Lib_Ch_AllType, "中文藏書量", "紙本圖書收藏冊數(冊)-中文紙本圖書-總類")]
        public decimal Lib_Ch_Total { get; set; }    // 中文紙本圖書總數

        [ChartColumn(ChartColumnType.Lib_Ch_Philosophy, "中文紙本圖書-哲學類", "紙本圖書收藏冊數(冊)-中文紙本圖書-哲學類")]
        public decimal Lib_Ch_Philosophy { get; set; }      // 中文紙本圖書-哲學類

        [ChartColumn(ChartColumnType.Lib_Ch_Religion, "中文紙本圖書-宗教類", "紙本圖書收藏冊數(冊)-中文紙本圖書-宗教類")]
        public decimal Lib_Ch_Religion { get; set; }        // 中文紙本圖書-宗教類

        [ChartColumn(ChartColumnType.Lib_Ch_Science, "中文紙本圖書-自然科學類", "紙本圖書收藏冊數(冊)-中文紙本圖書-自然科學類")]
        public decimal Lib_Ch_Science { get; set; }         // 中文紙本圖書-自然科學類

        [ChartColumn(ChartColumnType.Lib_Ch_Applied, "中文紙本圖書-應用科學類", "紙本圖書收藏冊數(冊)-中文紙本圖書-應用科學類")]
        public decimal Lib_Ch_Applied { get; set; }         // 中文紙本圖書-應用科學類

        [ChartColumn(ChartColumnType.Lib_Ch_Social, "中文紙本圖書-社會科學類", "紙本圖書收藏冊數(冊)-中文紙本圖書-社會科學類")]
        public decimal Lib_Ch_Social { get; set; }          // 中文紙本圖書-社會科學類

        [ChartColumn(ChartColumnType.Lib_Ch_History, "中文紙本圖書-史地類", "紙本圖書收藏冊數(冊)-中文紙本圖書-史地類(含世界史地類)")]
        public decimal Lib_Ch_History { get; set; }         // 中文紙本圖書-史地類

        [ChartColumn(ChartColumnType.Lib_Ch_Language, "中文紙本圖書-語言文學類", "紙本圖書收藏冊數(冊)-中文紙本圖書-語言文學類")]
        public decimal Lib_Ch_Language { get; set; }        // 中文紙本圖書-語言文學類

        [ChartColumn(ChartColumnType.Lib_Ch_Art, "中文紙本圖書-藝術類", "紙本圖書收藏冊數(冊)-中文紙本圖書-藝術類")]
        public decimal Lib_Ch_Art { get; set; }             // 中文紙本圖書-藝術類

        [ChartColumn(ChartColumnType.Lib_En_Total, "外文藏書量", "紙本圖書收藏冊數(冊)-外文紙本圖書-總類")]
        public decimal Lib_En_Total { get; set; }           // 外文紙本圖書總數

        [ChartColumn(ChartColumnType.Lib_E_Journals, "電子期刊數", "電子資料可使用量-電子期刊(種)")]
        public decimal Lib_E_Journals { get; set; }         // 電子期刊數

        [ChartColumn(ChartColumnType.Lib_E_Book, "電子書數", "電子資料可使用量-電子書(冊)")]
        public decimal Lib_E_Book { get; set; }             // 電子書數

        [ChartColumn(ChartColumnType.Lib_Paper_Ch, "中文報紙期刊", "現期書報(種)期刊-中日文(種)")]
        public decimal Lib_Paper_Ch { get; set; }           // 中文報紙期刊

        [ChartColumn(ChartColumnType.Lib_Paper_En, "西文報紙期刊", "現期書報(種)期刊-西文(種)")]
        public decimal Lib_Paper_En { get; set; }           // 西文報紙期刊

        // 用於圖表顯示的通用欄位
        public decimal Lib_AV { get; set; }                 // 用於圖表顯示的通用欄位

        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>(); // 其他數值欄位
    }

    /// <summary>
    /// 系統組-系統登入次數
    /// </summary>
    public class TIS_SysLoginCnt
    {
        public string REQ_APPID { get; set; }  // 系統名

        [ChartColumn(ChartColumnType.TIS_LoginCnt, "登入次數", "cnt")]
        public decimal TIS_LoginCnt { get; set; }          // 登入次數

        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>();
    }


    /// <summary>
    /// 系統組-系統開發數
    /// </summary>
    public class TIS_SysDevCnt
    {
        public string Time { get; set; }     // 時間

        [ChartColumn(ChartColumnType.TIS_SysCnt, "系統開發數", "自行開發系統數")] // 顯示欄位名稱 / Excel 欄位名稱
        public decimal TIS_SysCnt { get; set; }   // 自行開發系統數

        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// 系統組-系統組需求表
    /// </summary>
    public class TIS_Form
    {
        public string FormType { get; set; }   // 表單類型

        [ChartColumn(ChartColumnType.TCS_FormCnt, "筆數", "筆數")]
        public decimal TCS_FormCnt { get; set; }     // 筆數

        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// 應用組-GPU 使用人數
    /// </summary>
    public class TIM_GPUser
    {
        public string Time { get; set; }     // 時間

        [ChartColumn(ChartColumnType.TIM_GPU_UserCnt, "GPU使用人數統計", "GPU使用人數統計")]
        public decimal TIM_GPU_UserCnt { get; set; }  // GPU 使用人數統計

        public decimal AccumulatedUserCount { get; set; }  // GPU 使用人數累計

        [ChartColumn(ChartColumnType.TIM_GPU_UserCnt2, "使用人次統計", "人次統計")]
        public decimal TIM_GPU_UserCnt2 { get; set; }  // 人次統計 (第二分頁)

        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// 應用組-GPU 使用率
    /// </summary>
    public class TIM_GPURate
    {
        public string Time { get; set; }     // 時間

        [ChartColumn(ChartColumnType.TIM_GPURateP, "GPU使用率", "GPU使用率")]
        public decimal TIM_GPURateP { get; set; }   // GPU 使用率（百分比）

        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// 應用組-本校資訊預算統計資料
    /// </summary>
    public class TIM_CenterMoney
    {
        public string Time { get; set; }              // 年度

        [ChartColumn(ChartColumnType.TIM_HardwareCost, "硬體設備費", "硬體設備費")]
        public decimal TIM_HardwareCost { get; set; } // 硬體設備費 

        [ChartColumn(ChartColumnType.TIM_SoftwarePurchase, "軟體購置費", "軟體購置費")]
        public decimal TIM_SoftwarePurchase { get; set; } // 軟體購置費 

        [ChartColumn(ChartColumnType.TIM_SystemDevCost, "系統開發費", "系統開發費")]
        public decimal TIM_SystemDevCost { get; set; } // 系統開發費 

        [ChartColumn(ChartColumnType.TIM_MaintenanceCost, "資訊操作維護費", "資訊操作維護費")]
        public decimal TIM_MaintenanceCost { get; set; } // 資訊操作維護費 

        [ChartColumn(ChartColumnType.TIM_RentalCost, "資訊設備租金", "資訊設備租金")]
        public decimal TIM_RentalCost { get; set; } // 資訊設備租金 

        [ChartColumn(ChartColumnType.TIM_CloudCost, "雲端服務費", "雲端服務費")]
        public decimal TIM_CloudCost { get; set; } // 雲端服務費 

        [ChartColumn(ChartColumnType.TIM_LicenseFee, "軟體使用費", "軟體使用費")]
        public decimal TIM_LicenseFee { get; set; } // 軟體使用費 

        [ChartColumn(ChartColumnType.TIM_DataCommCost, "數據通訊費", "數據通訊費")]
        public decimal TIM_DataCommCost { get; set; } // 數據通訊費 

        [ChartColumn(ChartColumnType.TIM_SuppliesCost, "電腦用品及耗材", "電腦用品及耗材")]
        public decimal TIM_SuppliesCost { get; set; } // 電腦用品及耗材 

        [ChartColumn(ChartColumnType.TIM_PCRoomTotal, "合計", "合計")]
        public decimal TIM_PCRoomTotal { get; set; } // 合計 
        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// 應用組-本校資訊預算統計資料(要刪除)
    /// </summary>
    public class TIM_CenterMoney2
    {
        public string Time { get; set; }              // 年度

        [ChartColumn(ChartColumnType.TIM_BusinessCost, "業務費", "業務費(NT$)")]
        public decimal TIM_BusinessCost{ get; set; } // 業務費 (NT$)

        [ChartColumn(ChartColumnType.TIM_EquipmentCost, "設備費", "設備費(NT$)")]
        public decimal TIM_EquipmentCost { get; set; } // 設備費 (NT$)

        [ChartColumn(ChartColumnType.TIM_SoftwareCost, "軟體費", "軟體費(NT$)")]
        public decimal TIM_SoftwareCost { get; set; }  // 軟體費 (NT$)

        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// 網路組-網路流量圖
    /// </summary>
    public class NetworkTrafficModel
    {
        /// <summary>
        /// 時間 (yyyy mm dd hh mm 格式)
        /// </summary>
        [JsonProperty("time")]
        public string Time { get; set; } = "";

        /// <summary>
        /// 入站流量 (整數)
        /// </summary>
        [JsonProperty("inbound")]
        public int Inbound { get; set; }

        /// <summary>
        /// 出站流量 (整數)
        /// </summary>
        [JsonProperty("outbound")]
        public int Outbound { get; set; }

        /// <summary>
        /// 原始日期時間 (用於排序)
        /// </summary>
        [JsonProperty("originalDateTime")]
        public DateTime OriginalDateTime { get; set; }

        /// <summary>
        /// 原始時間格式 (用於表格顯示，格式：yyyy/M/d HH:mm)
        /// </summary>
        [JsonProperty("originalTimeFormat")]
        public string OriginalTimeFormat { get; set; } = "";
    }


    public class TIN
    {
        /// <summary>
        /// 時間 (HH:mm 格式)
        /// </summary>
        [JsonProperty("time")]
        public string Time { get; set; } = "";

        /// <summary>
        /// 入站流量 (整數)
        /// </summary>
        [JsonProperty("inbound")]
        public int Inbound { get; set; }

        /// <summary>
        /// 出站流量 (整數)
        /// </summary>
        [JsonProperty("outbound")]
        public int Outbound { get; set; }

        /// <summary>
        /// 原始日期時間 (用於排序)
        /// </summary>
        [JsonProperty("originalDateTime")]
        public DateTime OriginalDateTime { get; set; }
    }
}
