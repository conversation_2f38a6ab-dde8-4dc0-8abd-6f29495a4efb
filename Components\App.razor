﻿@inject IWebHostEnvironment env
@inject IHttpContextAccessor HttpContextAccessor
@inject IConfiguration Configuration

@code {
    private string nonce;

    protected override void OnInitialized()
    {
        nonce = HttpContextAccessor.HttpContext?.Items["cspNonce"] as string;
    }
}

<!DOCTYPE html>
<html lang="zh-Hant-TW">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="@Configuration["SysSetting:SysUrl"]" />
    <link rel="icon" type="image/svg+xml" href="/favicon.png" />
    
    <!-- BS5 -->
    <link rel="stylesheet" href="lib/bootstrap/bootstrap2.css">
    <link rel="stylesheet" href="Dashboard_Yuntech.styles.css" />
    <link rel="stylesheet" href="css/app4.css" />
    <!-- 插件 -->
    <link rel="stylesheet" href="lib/sweetalert2/sweetalert2.min.css" />
    <link rel="stylesheet" href="lib/air-datepicker-3/dist/air-datepicker.css" /> <!-- 日期插件 -->
    <link rel="stylesheet" href="lib/fancybox/fancybox.css" /> <!-- 圖片燈箱 -->
    <link rel="stylesheet" href="lib/select2/select2.min.css" /> <!-- select2 -->
    <link rel="stylesheet" href="lib/animate/animate.min.css" /> <!-- 動畫 -->

    <!-- 美麗的SVG -->
    <link href="fontawesome6/css/fontawesome.css" rel="stylesheet">
    <link href="fontawesome6/css/brands.css" rel="stylesheet">
    <link href="fontawesome6/css/solid.css" rel="stylesheet">

    <!--UI Css-->
    <link href="_content/Blazorise.Icons.FontAwesome/v6/css/all.min.css" rel="stylesheet">
    <link href="_content/Blazorise/blazorise.css?v=*******" rel="stylesheet" />
    <link href="_content/Blazorise.Bootstrap5/blazorise.bootstrap5.css?v=*******" rel="stylesheet" />
    
    <!-- 我的css -->
    <link rel="stylesheet" href="css/dashboard.css" />
    <link rel="stylesheet" href="css/myCss9.css" />
    <HeadOutlet />
</head>

<body>
    <Routes />
    <script src="_framework/blazor.web.js"></script>
    <script src="lib/jquery/jquery-3.7.1.min.js"></script>
    <script src="lib/bootstrap/bootstrap.min.js"></script>
    <script src="lib/sweetalert2/sweetalert2.min.js"></script>
    <script src="lib/air-datepicker-3/dist/air-datepicker.js"></script>
    <script src="lib/air-datepicker-3/dist/locale/zh.js"></script>
    <!--圖表-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

    <script src="lib/fancybox/fancybox.umd.js"></script>
    <script src="lib/select2/select2.min.js"></script>

    <!--自訂JS-->
    <script src="js/BaseJs11.js"></script>
    <script src="js/LayOut3.js"></script>
</body>

</html>

@code {
    [CascadingParameter] HttpContext HttpContext { get; set; } = default!;
    IComponentRenderMode? RenderModeForPage => 
        HttpContext.Request.Path.StartsWithSegments("/login") || HttpContext.Request.Path.StartsWithSegments("/logout") ?
            null : RenderMode.InteractiveServer;
}